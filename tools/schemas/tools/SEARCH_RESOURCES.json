{"tool_name": "SEARCH_RESOURCES", "extraction_timestamp": null, "data_integrity_principle": null, "schema": {"tool_name": "SEARCH_RESOURCES", "description": "Search resources across the cluster", "parameters": [{"name": "cluster", "type": "string", "description": "Cluster name", "required": true}, {"name": "query", "type": "string", "description": "Search query (name, label, annotation pattern)", "required": true}, {"name": "kinds", "type": "string", "description": "Kinds of resources to search (comma-separated)", "required": false}, {"name": "matchAnnotations", "type": "boolean", "description": "Whether to match annotations", "required": false}, {"name": "matchLabels", "type": "boolean", "description": "Whether to match labels", "required": false}, {"name": "namespaces", "type": "string", "description": "Namespaces to search within (comma-separated)", "required": false}]}}