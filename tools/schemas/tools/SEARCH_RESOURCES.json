{"tool_name": "SEARCH_RESOURCES", "schema": "Thought: The user is asking for the schema of the `SEARCH_RESOURCES` tool. This information is available in the OpenAPI specification of the tool. I need to describe the parameters of the `SEARCH_RESOURCES` function, including their types and whether they are required. I will present this information in a JSON format.\nFinal Answer: ```json\n{\n  \"tool_name\": \"SEARCH_RESOURCES\",\n  \"description\": \"Search resources across the cluster\",\n  \"parameters\": [\n    {\n      \"name\": \"cluster\",\n      \"type\": \"string\",\n      \"description\": \"Cluster name\",\n      \"required\": true\n    },\n    {\n      \"name\": \"query\",\n      \"type\": \"string\",\n      \"description\": \"Search query (name, label, annotation pattern)\",\n      \"required\": true\n    },\n    {\n      \"name\": \"kinds\",\n      \"type\": \"string\",\n      \"description\": \"Kinds of resources to search (comma-separated)\",\n      \"required\": false\n    },\n    {\n      \"name\": \"matchAnnotations\",\n      \"type\": \"boolean\",\n      \"description\": \"Whether to match annotations\",\n      \"required\": false\n    },\n    {\n      \"name\": \"matchLabels\",\n      \"type\": \"boolean\",\n      \"description\": \"Whether to match labels\",\n      \"required\": false\n    },\n    {\n      \"name\": \"namespaces\",\n      \"type\": \"string\",\n      \"description\": \"Namespaces to search within (comma-separated)\",\n      \"required\": false\n    }\n  ]\n}\n```"}