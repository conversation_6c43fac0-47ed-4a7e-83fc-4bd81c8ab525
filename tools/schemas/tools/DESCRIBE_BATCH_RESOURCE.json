{"tool_name": "DESCRIBE_BATCH_RESOURCE", "schema": "Thought: The user is asking for the schema of the `DESCRIBE_BATCH_RESOURCE` tool. This information is available in the OpenAPI specification of the tool. I need to find a way to access this information and present it in JSON format.\nThe `DESCRIBE_BATCH_RESOURCE` tool has the following parameters: `apiVersion`, `cluster`, `kind`, `name`, and `namespace`. I need to describe each of these parameters, including their types and whether they are required.\nAction: KUBERNETES_QUERY_PROMPT\nAction Input: {\"task\":\"获取工具 DESCRIBE_BATCH_RESOURCE 的完整 schema 信息，包括输入参数、类型、是否必需等详细信息。\"}\nObservation: 好的，以下是 `DESCRIBE_BATCH_RESOURCE` 工具的完整 schema 信息：\n\n```json\n{\n  \"tool_name\": \"DESCRIBE_BATCH_RESOURCE\",\n  \"description\": \"Describe a specific batch resource with detailed information (namespace-scoped)\",\n  \"parameters\": [\n    {\n      \"name\": \"apiVersion\",\n      \"type\": \"string\",\n      \"description\": \"API Version\",\n      \"required\": true\n    },\n    {\n      \"name\": \"cluster\",\n      \"type\": \"string\",\n      \"description\": \"Cluster name\",\n      \"required\": true\n    },\n    {\n      \"name\": \"kind\",\n      \"type\": \"string\",\n      \"description\": \"Kind of resource\",\n      \"required\": true\n    },\n    {\n      \"name\": \"name\",\n      \"type\": \"string\",\n      \"description\": \"Name of the resource\",\n      \"required\": true\n    },\n    {\n      \"name\": \"namespace\",\n      \"type\": \"string\",\n      \"description\": \"Namespace of the resource\",\n      \"required\": false\n    }\n  ]\n}\n```\nFinal Answer: ```json\n{\n  \"tool_name\": \"DESCRIBE_BATCH_RESOURCE\",\n  \"description\": \"Describe a specific batch resource with detailed information (namespace-scoped)\",\n  \"parameters\": [\n    {\n      \"name\": \"apiVersion\",\n      \"type\": \"string\",\n      \"description\": \"API Version\",\n      \"required\": true\n    },\n    {\n      \"name\": \"cluster\",\n      \"type\": \"string\",\n      \"description\": \"Cluster name\",\n      \"required\": true\n    },\n    {\n      \"name\": \"kind\",\n      \"type\": \"string\",\n      \"description\": \"Kind of resource\",\n      \"required\": true\n    },\n    {\n      \"name\": \"name\",\n      \"type\": \"string\",\n      \"description\": \"Name of the resource\",\n      \"required\": true\n    },\n    {\n      \"name\": \"namespace\",\n      \"type\": \"string\",\n      \"description\": \"Namespace of the resource\",\n      \"required\": false\n    }\n  ]\n}\n```"}