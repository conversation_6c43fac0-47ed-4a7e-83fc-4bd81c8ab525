{"tool_name": "DESCRIBE_APPS_RESOURCE", "schema": "Thought: The user is asking for the schema information of the `DESCRIBE_APPS_RESOURCE` tool. This information is available in the OpenAPI specification of the tool. I need to find the definition of this tool and extract its parameters, types, and whether they are required. I will then format this information as a JSON object.\nAction: default_api.DESCRIBE_APPS_RESOURCE.api_schema()\nObservation: {'name': 'DESCRIBE_APPS_RESOURCE', 'description': 'Describe a specific apps resource with detailed information (namespace-scoped)', 'parameters': {'type': 'object', 'properties': {'apiVersion': {'type': 'string'}, 'cluster': {'type': 'string'}, 'kind': {'type': 'string'}, 'name': {'type': 'string'}, 'namespace': {'type': 'string'}}, 'required': ['apiVersion', 'cluster', 'kind', 'name']}}\nThought: The user asked for the complete schema information of the `DESCRIBE_APPS_RESOURCE` tool, including input parameters, types, and whether they are required, in JSON format. I have already retrieved this information using `default_api.DESCRIBE_APPS_RESOURCE.api_schema()`. The `Observation` directly provides the requested schema in a dictionary format, which can be directly converted to JSON.\nFinal Answer: ```json\n{\n  \"name\": \"DESCRIBE_APPS_RESOURCE\",\n  \"description\": \"Describe a specific apps resource with detailed information (namespace-scoped)\",\n  \"parameters\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"apiVersion\": {\n        \"type\": \"string\"\n      },\n      \"cluster\": {\n        \"type\": \"string\"\n      },\n      \"kind\": {\n        \"type\": \"string\"\n      },\n      \"name\": {\n        \"type\": \"string\"\n      },\n      \"namespace\": {\n        \"type\": \"string\"\n      }\n    },\n    \"required\": [\n      \"apiVersion\",\n      \"cluster\",\n      \"kind\",\n      \"name\"\n    ]\n  }\n}\n```"}