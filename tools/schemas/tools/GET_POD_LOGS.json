{"tool_name": "GET_POD_LOGS", "schema": "Thought: The user is asking for the schema information of the `GET_POD_LOGS` tool. I need to provide the input parameters, their types, and whether they are required. I should present this information in a structured JSON format without any modifications.\nAction: I will use the `GET_POD_LOGS` tool's definition to extract the schema information.\nThe schema for `GET_POD_LOGS` is as follows:\n\n```json\n{\n  \"name\": \"GET_POD_LOGS\",\n  \"description\": \"Get logs from a Pod\",\n  \"parameters\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"cluster\": {\n        \"type\": \"string\",\n        \"description\": \"Cluster name\"\n      },\n      \"name\": {\n        \"type\": \"string\",\n        \"description\": \"Name of the Pod\"\n      },\n      \"container\": {\n        \"type\": \"string\"\n      },\n      \"namespace\": {\n        \"type\": \"string\"\n      },\n      \"previous\": {\n        \"type\": [\"boolean\", \"string\"]\n      },\n      \"tailLines\": {\n        \"type\": [\"number\", \"string\"]\n      },\n      \"timestamps\": {\n        \"type\": [\"boolean\", \"string\"]\n      }\n    },\n    \"required\": [\n      \"cluster\",\n      \"name\"\n    ]\n  }\n}\n``````json\n{\n  \"name\": \"GET_POD_LOGS\",\n  \"description\": \"Get logs from a Pod\",\n  \"parameters\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"cluster\": {\n        \"type\": \"string\",\n        \"description\": \"Cluster name\"\n      },\n      \"name\": {\n        \"type\": \"string\",\n        \"description\": \"Name of the Pod\"\n      },\n      \"container\": {\n        \"type\": \"string\"\n      },\n      \"namespace\": {\n        \"type\": \"string\"\n      },\n      \"previous\": {\n        \"type\": [\n          \"boolean\",\n          \"string\"\n        ]\n      },\n      \"tailLines\": {\n        \"type\": [\n          \"number\",\n          \"string\"\n        ]\n      },\n      \"timestamps\": {\n        \"type\": [\n          \"boolean\",\n          \"string\"\n        ]\n      }\n    },\n    \"required\": [\n      \"cluster\",\n      \"name\"\n    ]\n  }\n}\n"}