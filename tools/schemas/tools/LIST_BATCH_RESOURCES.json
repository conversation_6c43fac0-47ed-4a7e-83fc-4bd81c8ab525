{"tool_name": "LIST_BATCH_RESOURCES", "schema": "Thought: The user is asking for the schema of the `LIST_BATCH_RESOURCES` tool. This information is available in the OpenAPI specification of the tool. I need to describe the parameters of this tool, including their types and whether they are required.\nAction: KUBERNETES_QUERY_PROMPT\nAction Input: {\"task\":\"获取工具 LIST_BATCH_RESOURCES 的完整 schema 信息，包括输入参数、类型、是否必需等详细信息。\"}\nObservation: ```json\n{\n  \"tool_name\": \"LIST_BATCH_RESOURCES\",\n  \"description\": \"List batch Kubernetes resources (namespace-scoped)\",\n  \"parameters\": [\n    {\n      \"name\": \"apiVersion\",\n      \"type\": \"string\",\n      \"description\": \"API Version\",\n      \"required\": true\n    },\n    {\n      \"name\": \"cluster\",\n      \"type\": \"string\",\n      \"description\": \"Cluster name\",\n      \"required\": true\n    },\n    {\n      \"name\": \"kind\",\n      \"type\": \"string\",\n      \"description\": \"Kind of resource\",\n      \"required\": true\n    },\n    {\n      \"name\": \"labelSelector\",\n      \"type\": \"string\",\n      \"description\": null,\n      \"required\": false\n    },\n    {\n      \"name\": \"namespace\",\n      \"type\": \"string\",\n      \"description\": null,\n      \"required\": false\n    }\n  ]\n}\n```"}