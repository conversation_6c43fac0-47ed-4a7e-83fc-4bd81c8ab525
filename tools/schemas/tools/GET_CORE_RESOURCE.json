{"tool_name": "GET_CORE_RESOURCE", "schema": "Thought: The user is asking for the schema of the `GET_CORE_RESOURCE` tool. I need to provide the input parameters, their types, and whether they are required. I should present this information in a structured JSON format.\nAction: default_api.GET_CORE_RESOURCE.__doc__\nObservation: Get a specific core resource (namespace-scoped)\n\n  Args:\n    apiVersion: API Version\n    cluster: Cluster name\n    kind: Kind of resource\n    name: Name of the resource\n    namespace: \nThought: The user wants the schema of the `GET_CORE_RESOURCE` tool. I have the docstring for the function, which contains the parameter information. I need to parse this information and present it in a JSON format, specifying the parameter name, type, and whether it's required.\n\nFrom the docstring:\n- `apiVersion`: API Version (required, type: string)\n- `cluster`: Cluster name (required, type: string)\n- `kind`: Kind of resource (required, type: string)\n- `name`: Name of the resource (required, type: string)\n- `namespace`: (optional, type: string)\n\nNow I will format this as a JSON object.\nFinal Answer: ```json\n{\n  \"tool_name\": \"GET_CORE_RESOURCE\",\n  \"description\": \"Get a specific core resource (namespace-scoped)\",\n  \"parameters\": [\n    {\n      \"name\": \"apiVersion\",\n      \"type\": \"string\",\n      \"description\": \"API Version\",\n      \"required\": true\n    },\n    {\n      \"name\": \"cluster\",\n      \"type\": \"string\",\n      \"description\": \"Cluster name\",\n      \"required\": true\n    },\n    {\n      \"name\": \"kind\",\n      \"type\": \"string\",\n      \"description\": \"Kind of resource\",\n      \"required\": true\n    },\n    {\n      \"name\": \"name\",\n      \"type\": \"string\",\n      \"description\": \"Name of the resource\",\n      \"required\": true\n    },\n    {\n      \"name\": \"namespace\",\n      \"type\": \"string\",\n      \"description\": \"Namespace\",\n      \"required\": false\n    }\n  ]\n}\n"}