{"tool_name": "LIST_USERS", "schema": "Thought: The user is asking for the schema of the `LIST_USERS` tool. I need to provide the input parameters, their types, and whether they are required. I should present this information in a JSON format without any modifications.\nAction: default_api.LIST_USERS\nAction Input: {}\nObservation: An error occurred: LIST_USERS missing 1 required positional arguments: 'name'\nThought: The `LIST_USERS` tool requires a `name` argument. I need to provide the schema of the tool, not execute it. I should use the `help` function or inspect the tool's definition to get its schema. Since I cannot directly access `help` or inspect the tool's definition in this environment, I will provide the information based on the tool definition I have.\nFinal Answer: ```json\n{\n  \"tool_name\": \"LIST_USERS\",\n  \"description\": \"List the user informations of an organization (also means 'system')\",\n  \"parameters\": [\n    {\n      \"name\": \"name\",\n      \"type\": \"string\",\n      \"description\": \"Name of the organization\",\n      \"required\": true\n    }\n  ]\n}\n```"}