{"tool_name": "DESCRIBE_AUTOSCALING_RESOURCE", "extraction_timestamp": null, "data_integrity_principle": null, "schema": {"tool_name": "DESCRIBE_AUTOSCALING_RESOURCE", "description": "Describe a specific autoscaling resource with detailed information (namespace-scoped)", "parameters": [{"name": "apiVersion", "type": "string", "description": "API Version", "required": true}, {"name": "cluster", "type": "string", "description": "Cluster name", "required": true}, {"name": "kind", "type": "string", "description": "Kind of resource", "required": true}, {"name": "name", "type": "string", "description": "Name of the resource", "required": true}, {"name": "namespace", "type": "string", "description": null, "required": false}]}}