{"tool_name": "DESCRIBE_AUTOSCALING_RESOURCE", "schema": "Thought: The user is asking for the schema of the `DESCRIBE_AUTOSCALING_RESOURCE` tool. This information is available in the OpenAPI specification of the tool. I need to present this information in a structured JSON format, including input parameters, their types, and whether they are required.\nAction: KUBERNETES_QUERY_PROMPT\nAction Input: {\"task\": \"获取工具 DESCRIBE_AUTOSCALING_RESOURCE 的完整 schema 信息，包括输入参数、类型、是否必需等详细信息。要求：1. 绝对不编造、修改、删减或压缩任何返回数据；2. 不改变返回数据，用JSON格式结构化返回\"}\nObservation: ```json\n{\n  \"tool_name\": \"DESCRIBE_AUTOSCALING_RESOURCE\",\n  \"description\": \"Describe a specific autoscaling resource with detailed information (namespace-scoped)\",\n  \"parameters\": [\n    {\n      \"name\": \"apiVersion\",\n      \"type\": \"string\",\n      \"description\": \"API Version\",\n      \"required\": true\n    },\n    {\n      \"name\": \"cluster\",\n      \"type\": \"string\",\n      \"description\": \"Cluster name\",\n      \"required\": true\n    },\n    {\n      \"name\": \"kind\",\n      \"type\": \"string\",\n      \"description\": \"Kind of resource\",\n      \"required\": true\n    },\n    {\n      \"name\": \"name\",\n      \"type\": \"string\",\n      \"description\": \"Name of the resource\",\n      \"required\": true\n    },\n    {\n      \"name\": \"namespace\",\n      \"type\": \"string\",\n      \"description\": null,\n      \"required\": false\n    }\n  ]\n}\n```\nFinal Answer: ```json\n{\n  \"tool_name\": \"DESCRIBE_AUTOSCALING_RESOURCE\",\n  \"description\": \"Describe a specific autoscaling resource with detailed information (namespace-scoped)\",\n  \"parameters\": [\n    {\n      \"name\": \"apiVersion\",\n      \"type\": \"string\",\n      \"description\": \"API Version\",\n      \"required\": true\n    },\n    {\n      \"name\": \"cluster\",\n      \"type\": \"string\",\n      \"description\": \"Cluster name\",\n      \"required\": true\n    },\n    {\n      \"name\": \"kind\",\n      \"type\": \"string\",\n      \"description\": \"Kind of resource\",\n      \"required\": true\n    },\n    {\n      \"name\": \"name\",\n      \"type\": \"string\",\n      \"description\": \"Name of the resource\",\n      \"required\": true\n    },\n    {\n      \"name\": \"namespace\",\n      \"type\": \"string\",\n      \"description\": null,\n      \"required\": false\n    }\n  ]\n}\n```"}