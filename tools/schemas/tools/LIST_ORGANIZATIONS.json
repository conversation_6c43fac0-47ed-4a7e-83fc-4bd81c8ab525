{"tool_name": "LIST_ORGANIZATIONS", "schema": "Thought: The user is asking for the schema of the `LIST_ORGANIZATIONS` tool. This information is available in the OpenAPI specification of the tool. I need to describe the parameters, their types, and whether they are required.\nAction: KUBERNETES_QUERY_PROMPT\nAction Input: {\"task\":\"获取工具 LIST_ORGANIZATIONS 的完整 schema 信息，包括输入参数、类型、是否必需等详细信息。\"}\nObservation: 无法直接获取工具的完整schema信息。但是，我可以告诉你 `LIST_ORGANIZATIONS` 工具的参数信息：\n\n**LIST_ORGANIZATIONS**\n\n*   **detail**:\n    *   类型: boolean 或 string\n    *   是否必需: 否\n*   **page**:\n    *   类型: float 或 string\n    *   是否必需: 否\n*   **size**:\n    *   类型: float 或 string\n    *   是否必需: 否\n*   **withAdmin**:\n    *   类型: boolean 或 string\n    *   是否必需: 否\n\n这些参数的描述如下：\n\n*   `detail`: 是否返回详细信息。\n*   `page`: 页码，用于分页查询。\n*   `size`: 每页返回的数量。\n*   `withAdmin`: 是否包含管理员信息。\n\n请注意，这些信息是根据工具的定义生成的，可能与实际的OpenAPI schema略有不同，但包含了所有关键信息。\nFinal Answer: ```json\n{\n  \"tool_name\": \"LIST_ORGANIZATIONS\",\n  \"description\": \"List organizations (also means 'systems') by paging. If you want to get all organizations, you need to call this tool repeatedly by incrementing the page parameter until the returned pageStatus.is_end is true.\",\n  \"parameters\": [\n    {\n      \"name\": \"detail\",\n      \"type\": \"boolean | string\",\n      \"required\": false,\n      \"description\": \"\"\n    },\n    {\n      \"name\": \"page\",\n      \"type\": \"float | string\",\n      \"required\": false,\n      \"description\": \"\"\n    },\n    {\n      \"name\": \"size\",\n      \"type\": \"float | string\",\n      \"required\": false,\n      \"description\": \"\"\n    },\n    {\n      \"name\": \"withAdmin\",\n      \"type\": \"boolean | string\",\n      \"required\": false,\n      \"description\": \"\"\n    }\n  ]\n}\n"}