{"tool_name": "GET_RBAC_RESOURCE", "schema": "Thought: The user is asking for the schema information of the `GET_RBAC_RESOURCE` tool. This information is available in the OpenAPI specification of the tool. I need to find a way to access this schema information and present it in a structured JSON format. Since I don't have a direct tool to get the schema of a tool, I will provide the information based on the function definition I have.\nFinal Answer: ```json\n{\n  \"tool_name\": \"GET_RBAC_RESOURCE\",\n  \"description\": \"Get a specific rbac.authorization.k8s.io resource (namespace-scoped)\",\n  \"parameters\": [\n    {\n      \"name\": \"apiVersion\",\n      \"type\": \"string\",\n      \"description\": \"API Version\",\n      \"required\": true\n    },\n    {\n      \"name\": \"cluster\",\n      \"type\": \"string\",\n      \"description\": \"Cluster name\",\n      \"required\": true\n    },\n    {\n      \"name\": \"kind\",\n      \"type\": \"string\",\n      \"description\": \"Kind of resource\",\n      \"required\": true\n    },\n    {\n      \"name\": \"name\",\n      \"type\": \"string\",\n      \"description\": \"Name of the resource\",\n      \"required\": true\n    },\n    {\n      \"name\": \"namespace\",\n      \"type\": \"string\",\n      \"description\": \"Namespace\",\n      \"required\": false\n    }\n  ]\n}\n```"}