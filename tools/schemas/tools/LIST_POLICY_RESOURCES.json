{"tool_name": "LIST_POLICY_RESOURCES", "extraction_timestamp": null, "data_integrity_principle": null, "schema": {"tool_name": "LIST_POLICY_RESOURCES", "description": "List policy Kubernetes resources (namespace-scoped)", "parameters": [{"name": "apiVersion", "type": "string", "description": "API Version", "required": true}, {"name": "cluster", "type": "string", "description": "Cluster name", "required": true}, {"name": "kind", "type": "string", "description": "Kind of resource", "required": true}, {"name": "labelSelector", "type": "string", "description": null, "required": false}, {"name": "namespace", "type": "string", "description": null, "required": false}]}}