{"tool_name": "LIST_POLICY_RESOURCES", "schema": "Thought: The user is asking for the schema of the `LIST_POLICY_RESOURCES` tool. This information is available in the tool definitions. I need to extract the function signature and format it as a JSON object, including parameter names, types, and whether they are required.```json\n{\n  \"tool_name\": \"LIST_POLICY_RESOURCES\",\n  \"description\": \"List policy Kubernetes resources (namespace-scoped)\",\n  \"parameters\": [\n    {\n      \"name\": \"apiVersion\",\n      \"type\": \"string\",\n      \"description\": \"API Version\",\n      \"required\": true\n    },\n    {\n      \"name\": \"cluster\",\n      \"type\": \"string\",\n      \"description\": \"Cluster name\",\n      \"required\": true\n    },\n    {\n      \"name\": \"kind\",\n      \"type\": \"string\",\n      \"description\": \"Kind of resource\",\n      \"required\": true\n    },\n    {\n      \"name\": \"labelSelector\",\n      \"type\": \"string\",\n      \"description\": null,\n      \"required\": false\n    },\n    {\n      \"name\": \"namespace\",\n      \"type\": \"string\",\n      \"description\": null,\n      \"required\": false\n    }\n  ]\n}\n"}