{"tool_name": "GET_EVENTS", "schema": "Thought: The user is asking for the schema of the `GET_EVENTS` tool. This information is available in the OpenAPI specification of the tool. I need to describe the parameters of the `GET_EVENTS` function, including their types and whether they are required.\nAction: KUBERNETES_QUERY_PROMPT\nAction Input: {\"task\": \"获取工具 GET_EVENTS 的完整 schema 信息，包括输入参数、类型、是否必需等详细信息。\"}\nObservation: 好的，以下是工具 `GET_EVENTS` 的完整 schema 信息：\n\n```json\n{\n  \"name\": \"GET_EVENTS\",\n  \"description\": \"Get events for a resource\",\n  \"parameters\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"apiVersion\": {\n        \"type\": \"string\",\n        \"description\": \"API Version\"\n      },\n      \"cluster\": {\n        \"type\": \"string\",\n        \"description\": \"Cluster name\"\n      },\n      \"kind\": {\n        \"type\": \"string\",\n        \"description\": \"Kind of resource\"\n      },\n      \"name\": {\n        \"type\": \"string\",\n        \"description\": \"Name of the resource\"\n      },\n      \"namespace\": {\n        \"type\": \"string\"\n      }\n    },\n    \"required\": [\n      \"apiVersion\",\n      \"cluster\",\n      \"kind\",\n      \"name\"\n    ]\n  }\n}\n```\nFinal Answer: ```json\n{\n  \"name\": \"GET_EVENTS\",\n  \"description\": \"Get events for a resource\",\n  \"parameters\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"apiVersion\": {\n        \"type\": \"string\",\n        \"description\": \"API Version\"\n      },\n      \"cluster\": {\n        \"type\": \"string\",\n        \"description\": \"Cluster name\"\n      },\n      \"kind\": {\n        \"type\": \"string\",\n        \"description\": \"Kind of resource\"\n      },\n      \"name\": {\n        \"type\": \"string\",\n        \"description\": \"Name of the resource\"\n      },\n      \"namespace\": {\n        \"type\": \"string\"\n      }\n    },\n    \"required\": [\n      \"apiVersion\",\n      \"cluster\",\n      \"kind\",\n      \"name\"\n    ]\n  }\n}\n"}