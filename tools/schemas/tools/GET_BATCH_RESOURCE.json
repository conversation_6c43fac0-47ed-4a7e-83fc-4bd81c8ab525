{"tool_name": "GET_BATCH_RESOURCE", "extraction_timestamp": null, "data_integrity_principle": null, "schema": {"tool_name": "GET_BATCH_RESOURCE", "description": "Get a specific batch resource (namespace-scoped)", "parameters": [{"name": "apiVersion", "type": "string", "required": true, "description": "API Version"}, {"name": "cluster", "type": "string", "required": true, "description": "Cluster name"}, {"name": "kind", "type": "string", "required": true, "description": "Kind of resource"}, {"name": "name", "type": "string", "required": true, "description": "Name of the resource"}, {"name": "namespace", "type": "string", "required": false, "description": null}]}}