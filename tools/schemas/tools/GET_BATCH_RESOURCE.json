{"tool_name": "GET_BATCH_RESOURCE", "schema": "Thought: The user is asking for the schema of the `GET_BATCH_RESOURCE` tool. I need to provide the input parameters, their types, and whether they are required. I will format this information as a JSON object.\nAction: default_api.GET_BATCH_RESOURCE.__doc__\nObservation: Get a specific batch resource (namespace-scoped)\n\n  Args:\n    apiVersion: API Version\n    cluster: Cluster name\n    kind: Kind of resource\n    name: Name of the resource\n    namespace: \nThought: The user is asking for the schema of the `GET_BATCH_RESOURCE` tool. I have retrieved the docstring for the tool, which contains the information about the arguments. I need to parse this information and present it in a structured JSON format, including the parameter name, type, and whether it's required.\n\nBased on the docstring:\n- `apiVersion`: API Version (string, required)\n- `cluster`: Cluster name (string, required)\n- `kind`: Kind of resource (string, required)\n- `name`: Name of the resource (string, required)\n- `namespace`: (string, optional)\n\nNow I will format this into a JSON object.\nFinal Answer: ```json\n{\n  \"tool_name\": \"GET_BATCH_RESOURCE\",\n  \"description\": \"Get a specific batch resource (namespace-scoped)\",\n  \"parameters\": [\n    {\n      \"name\": \"apiVersion\",\n      \"type\": \"string\",\n      \"required\": true,\n      \"description\": \"API Version\"\n    },\n    {\n      \"name\": \"cluster\",\n      \"type\": \"string\",\n      \"required\": true,\n      \"description\": \"Cluster name\"\n    },\n    {\n      \"name\": \"kind\",\n      \"type\": \"string\",\n      \"required\": true,\n      \"description\": \"Kind of resource\"\n    },\n    {\n      \"name\": \"name\",\n      \"type\": \"string\",\n      \"required\": true,\n      \"description\": \"Name of the resource\"\n    },\n    {\n      \"name\": \"namespace\",\n      \"type\": \"string\",\n      \"required\": false,\n      \"description\": null\n    }\n  ]\n}\n"}