{"tool_name": "EXPLAIN_RESOURCE", "schema": "Thought: The user is asking for the schema of the `EXPLAIN_RESOURCE` tool. I need to provide the input parameters, their types, and whether they are required. I will format this information as a JSON object.\nAction: KUBERNETES_QUERY_PROMPT\nAction Input: {\"task\": \"获取工具 EXPLAIN_RESOURCE 的完整 schema 信息，包括输入参数、类型、是否必需等详细信息。要求：1. 绝对不编造、修改、删减或压缩任何返回数据；2. 不改变返回数据，用JSON格式结构化返回\"}\nObservation: ```json\n{\n  \"tool_name\": \"EXPLAIN_RESOURCE\",\n  \"description\": \"Explain resource structure\",\n  \"parameters\": [\n    {\n      \"name\": \"apiVersion\",\n      \"type\": \"string\",\n      \"description\": \"API Version\",\n      \"required\": true\n    },\n    {\n      \"name\": \"cluster\",\n      \"type\": \"string\",\n      \"description\": \"Cluster name\",\n      \"required\": true\n    },\n    {\n      \"name\": \"kind\",\n      \"type\": \"string\",\n      \"description\": \"Kind of resource\",\n      \"required\": true\n    },\n    {\n      \"name\": \"field\",\n      \"type\": \"string\",\n      \"description\": null,\n      \"required\": false\n    },\n    {\n      \"name\": \"recursive\",\n      \"type\": \"boolean | string\",\n      \"description\": null,\n      \"required\": false\n    }\n  ]\n}\n```\nThought: I have successfully retrieved the schema information for the `EXPLAIN_RESOURCE` tool, including all the requested details (input parameters, types, and whether they are required). The information is structured in JSON format as requested.\nFinal Answer: ```json\n{\n  \"tool_name\": \"EXPLAIN_RESOURCE\",\n  \"description\": \"Explain resource structure\",\n  \"parameters\": [\n    {\n      \"name\": \"apiVersion\",\n      \"type\": \"string\",\n      \"description\": \"API Version\",\n      \"required\": true\n    },\n    {\n      \"name\": \"cluster\",\n      \"type\": \"string\",\n      \"description\": \"Cluster name\",\n      \"required\": true\n    },\n    {\n      \"name\": \"kind\",\n      \"type\": \"string\",\n      \"description\": \"Kind of resource\",\n      \"required\": true\n    },\n    {\n      \"name\": \"field\",\n      \"type\": \"string\",\n      \"description\": null,\n      \"required\": false\n    },\n    {\n      \"name\": \"recursive\",\n      \"type\": \"boolean | string\",\n      \"description\": null,\n      \"required\": false\n    }\n  ]\n}\n"}