{"tool_name": "ANALYZE_POD_LOGS", "extraction_timestamp": "2025-06-20", "data_integrity_principle": "绝对不编造、修改、删减或压缩任何返回数据", "schema": "Thought: The user is asking for the schema information of the `ANALYZE_POD_LOGS` tool. This information is available in the OpenAPI specification. I need to describe the parameters of this tool, including their names, types, and whether they are required.\nFinal Answer: ANALYZE_POD_LOGS 工具的 schema 信息如下：\n\n*   **cluster** (string, 必需): 集群名称。\n*   **name** (string, 必需): Pod 的名称。\n*   **container** (string, 可选): 容器名称。\n*   **errorPattern** (string, 可选): 错误匹配模式。\n*   **namespace** (string, 可选): 命名空间。\n*   **previous** (boolean 或 string, 可选): 是否获取上一个 Pod 的日志。\n*   **prompt** (string, 可选): 提示信息。\n*   **tailLines** (number 或 string, 可选): 尾部行数。"}