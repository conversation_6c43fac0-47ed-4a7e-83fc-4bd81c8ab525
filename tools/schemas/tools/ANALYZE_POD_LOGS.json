{"tool_name": "ANALYZE_POD_LOGS", "schema": "```json\n{\n  \"name\": \"ANALYZE_POD_LOGS\",\n  \"description\": \"Analyze logs from a Pod and provide insights\",\n  \"parameters\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"cluster\": {\n        \"type\": \"string\",\n        \"description\": \"Cluster name\"\n      },\n      \"name\": {\n        \"type\": \"string\",\n        \"description\": \"Name of the Pod\"\n      },\n      \"container\": {\n        \"type\": \"string\"\n      },\n      \"errorPattern\": {\n        \"type\": \"string\"\n      },\n      \"namespace\": {\n        \"type\": \"string\"\n      },\n      \"previous\": {\n        \"type\": \"string\",\n        \"format\": \"boolean\"\n      },\n      \"prompt\": {\n        \"type\": \"string\"\n      },\n      \"tailLines\": {\n        \"type\": \"string\",\n        \"format\": \"number\"\n      }\n    },\n    \"required\": [\n      \"cluster\",\n      \"name\"\n    ]\n  }\n}\n```"}