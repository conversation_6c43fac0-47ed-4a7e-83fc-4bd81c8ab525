{"tool_name": "ANALYZE_POD_LOGS", "schema": "```json\n{\n  \"name\": \"ANALYZE_POD_LOGS\",\n  \"description\": \"Analyze logs from a Pod and provide insights\",\n  \"parameters\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"cluster\": {\n        \"type\": \"string\"\n      },\n      \"name\": {\n        \"type\": \"string\"\n      },\n      \"container\": {\n        \"type\": \"string\"\n      },\n      \"errorPattern\": {\n        \"type\": \"string\"\n      },\n      \"namespace\": {\n        \"type\": \"string\"\n      },\n      \"previous\": {\n        \"type\": [\n          \"boolean\",\n          \"string\"\n        ]\n      },\n      \"prompt\": {\n        \"type\": \"string\"\n      },\n      \"tailLines\": {\n        \"type\": [\n          \"number\",\n          \"string\"\n        ]\n      }\n    },\n    \"required\": [\n      \"cluster\",\n      \"name\"\n    ]\n  }\n}\n"}