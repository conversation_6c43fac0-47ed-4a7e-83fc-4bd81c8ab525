{"tool_name": "ANALYZE_POD_LOGS", "extraction_timestamp": "2025-06-20", "data_integrity_principle": "绝对不编造、修改、删减或压缩任何返回数据", "schema": "Thought: The user is asking for the schema information of the `ANALYZE_POD_LOGS` tool. This information is available in the tool's definition. I need to extract the parameters, their types, and whether they are required.\nFinal Answer: ANALYZE_POD_LOGS 工具的 schema 信息如下：\n\n*   **cluster** (string, 必需): Cluster name\n*   **name** (string, 必需): Name of the Pod\n*   **container** (string, 可选): Container name\n*   **errorPattern** (string, 可选): Error pattern to search for in logs\n*   **namespace** (string, 可选): Namespace of the Pod\n*   **previous** (boolean, 可选): Whether to get the logs for the previous container\n*   **prompt** (string, 可选): Prompt for log analysis\n*   **tailLines** (number, 可选): Number of lines to tail from the end of the log"}