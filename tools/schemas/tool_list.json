{"total_count": 55, "extraction_timestamp": "2025-06-20", "data_integrity_principle": "绝对不编造、修改、删减或压缩任何返回数据", "tools": ["ANALYZE_POD_LOGS", "DESCRIBE_APIEXTENSIONS_RESOURCE", "DESCRIBE_APPS_RESOURCE", "DESCRIBE_AUTOSCALING_RESOURCE", "DESCRIBE_BATCH_RESOURCE", "DESCRIBE_CLUSTER", "DESCRIBE_CORE_RESOURCE", "DESCRIBE_NETWORKING_RESOURCE", "DESCRIBE_POLICY_RESOURCE", "DESCRIBE_RBAC_RESOURCE", "DESCRIBE_STORAGE_RESOURCE", "DESC_ORGANIZATION", "DIFF_MANIFEST", "EXPLAIN_RESOURCE", "GET_APIEXTENSIONS_RESOURCE", "GET_API_RESOURCES", "GET_APPS_RESOURCE", "GET_AUTOSCALING_RESOURCE", "GET_BATCH_RESOURCE", "GET_CLUSTER_INFO", "GET_CORE_RESOURCE", "GET_EVENTS", "GET_NETWORKING_RESOURCE", "GET_NODE_METRICS", "GET_POD_LOGS", "GET_POD_METRICS", "GET_POLICY_RESOURCE", "GET_RBAC_RESOURCE", "GET_RESOURCE_METRICS", "GET_STORAGE_RESOURCE", "GET_TOP_CONSUMERS", "LIST_APIEXTENSIONS_RESOURCES", "LIST_APPS_RESOURCES", "LIST_AUTOSCALING_RESOURCES", "LIST_BATCH_RESOURCES", "LIST_CLUSTERS", "LIST_CORE_RESOURCES", "LIST_NAMESPACES", "LIST_NETWORKING_RESOURCES", "LIST_NODES", "LIST_ORGANIZATIONS", "LIST_POLICY_RESOURCES", "LIST_RBAC_RESOURCES", "LIST_STORAGE_RESOURCES", "LIST_USERS", "SEARCH_RESOURCES", "VALIDATE_MANIFEST", "TROUBLESHOOT_PODS_PROMPT", "TROUBLESHOOT_NODES_PROMPT", "TROUBLESHOOT_NETWORK_PROMPT", "CLUSTER_RESOURCE_USAGE", "NODE_RESOURCE_USAGE", "POD_RESOURCE_USAGE", "KUBERNETES_YAML_PROMPT", "KUBERNETES_QUERY_PROMPT"]}